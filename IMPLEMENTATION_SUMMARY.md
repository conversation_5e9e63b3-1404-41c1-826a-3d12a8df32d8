# LBS接口切换功能实现总结

## 需求概述

实现将代码中使用`querydistancebatch`方法的地方切换为`queryEstimateRouteWithBufferTimeParallel`，通过以下条件进行智能切换：

1. **城市判断**：只有top20城市才进行切换
2. **高峰期判断**：基于用车时间判断是否在出发高峰期
3. **开关控制**：支持直接切换和异步埋点监控两种模式

## 实现方案

### 1. 配置层面

**修改文件：** `DelayDspCommonQConfig.java`

新增三个配置项：
- `top20CitiesForNewLbsApi`：支持新LBS接口的top20城市列表
- `directSwitchToNewLbsApi`：直接切换开关，配置的城市直接使用新接口
- `asyncMonitorNewLbsApi`：异步监控开关，配置的城市异步调用新接口进行对比

新增三个判断方法：
- `isTop20CityForNewLbsApi(Integer cityId)`：判断城市是否在top20列表中
- `isDirectSwitchToNewLbsApi(Integer cityId)`：判断是否开启直接切换
- `isAsyncMonitorNewLbsApi(Integer cityId)`：判断是否开启异步监控

### 2. 业务逻辑层面

**修改文件：** `GeoGatewayImpl.java`

#### 主要修改：

1. **新增依赖注入**：
   - `DcsMapDomainServiceProxy`：新LBS接口代理
   - `DelayDspCommonQConfig`：配置管理
   - `LbsBufferConfig`：高峰期时间配置

2. **修改queryRoutes方法**：
   - 增加切换判断逻辑
   - 根据判断结果选择使用新接口或旧接口

3. **新增核心方法**：
   - `shouldUseNewLbsApi()`：综合判断是否使用新接口
   - `isInPeakHours()`：判断是否在高峰期
   - `queryRoutesWithNewApi()`：调用新接口（框架实现）
   - `asyncMonitorNewLbsApi()`：异步监控新接口（框架实现）

#### 切换逻辑：

```java
// 判断是否需要切换到新接口
if (shouldUseNewLbsApi(cityId, list)) {
    routes.addAll(queryRoutesWithNewApi(cityId, list));
} else {
    // 使用旧接口
    QueryDistanceBatchRequestType request = buildQueryDistanceBatchRequestType(cityId, list);
    QueryDistanceBatchResponseType response = ochGeoServiceProxy.queryDistanceBatch(request);
    routes.addAll(buildRoute(response));
}
```

#### 判断优先级：

1. 城市必须在top20列表中
2. 当前时间必须在高峰期内
3. 直接切换开关 > 异步监控开关 > 使用旧接口

### 3. 高峰期时间判断

利用现有的`LbsBufferConfig`配置来判断高峰期：
- 获取城市对应的时间段配置
- 将当前时间与配置的`startTime`和`endTime`进行比较
- 支持跨天时间段（如20:00-08:00）

### 4. 监控埋点

添加了完整的监控指标：
- `geo.new.lbs.api.direct.switch`：直接切换次数
- `geo.new.lbs.api.async.monitor`：异步监控触发次数
- `geo.new.lbs.api.peak.hours.match`：高峰期匹配次数
- `geo.new.lbs.api.call.success`：新接口调用成功次数
- `geo.new.lbs.api.call.error`：新接口调用失败次数
- `geo.new.lbs.api.fallback.to.old`：降级到旧接口次数

### 5. 异常处理

- 配置异常时使用旧接口
- 新接口调用失败时自动降级到旧接口
- 完整的异常日志记录

## 测试验证

**创建文件：** `GeoGatewayImplNewApiTest.java`

覆盖以下测试场景：
1. 非top20城市使用旧接口
2. 非高峰期使用旧接口
3. 直接切换开关开启时使用新接口
4. 异步监控开关开启时使用旧接口但触发异步监控
5. 异常情况下的降级处理

## 配置示例

**创建文件：** `delay_dsp_common_new_lbs_api.properties`

提供了详细的配置示例和使用场景说明：
- 灰度测试场景
- 全量监控场景
- 逐步切换场景

## 部署指南

**创建文件：** `README_NEW_LBS_API.md`

包含：
- 功能详细说明
- 配置方法
- 监控指标说明
- 部署注意事项

## 待完善事项

由于无法获取新接口`QueryEstimateRouteWithBufferTimeParallelRequestType`和`QueryEstimateRouteWithBufferTimeParallelResponseType`的具体结构，以下部分需要后续完善：

1. **新接口请求构建**：`queryRoutesWithNewApi()`方法中的请求对象构建
2. **新接口响应解析**：将新接口响应转换为`Route`对象的逻辑
3. **异步监控实现**：`asyncMonitorNewLbsApi()`方法中的真正异步调用和结果对比逻辑

## 优势特点

1. **渐进式切换**：支持从异步监控到直接切换的渐进式部署
2. **智能判断**：基于城市和高峰期的智能切换逻辑
3. **完整监控**：全面的监控指标便于观察切换效果
4. **异常安全**：完善的异常处理和降级机制
5. **配置灵活**：支持细粒度的城市级别配置

## 使用建议

1. **第一阶段**：配置异步监控，观察新旧接口的差异
2. **第二阶段**：对稳定的城市开启直接切换
3. **第三阶段**：根据监控数据逐步扩大切换范围

这个实现方案既保证了系统的稳定性，又提供了灵活的切换策略，可以根据实际业务需求进行调整。
