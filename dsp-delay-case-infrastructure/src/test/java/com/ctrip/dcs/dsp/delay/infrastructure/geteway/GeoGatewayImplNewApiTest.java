package com.ctrip.dcs.dsp.delay.infrastructure.geteway;

import com.ctrip.dcs.dsp.delay.infrastructure.soa.DcsMapDomainServiceProxy;
import com.ctrip.dcs.dsp.delay.infrastructure.soa.OchGeoServiceProxy;
import com.ctrip.dcs.dsp.delay.infrastructure.trocks.TRocksProviderProxy;
import com.ctrip.dcs.dsp.delay.limit.LbsRateLimiter;
import com.ctrip.dcs.dsp.delay.model.Position;
import com.ctrip.dcs.dsp.delay.model.Route;
import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig;
import com.ctrip.dcs.dsp.delay.qconfig.LbsBufferConfig;
import com.ctrip.dcs.dsp.delay.qconfig.value.LbsBufferValueVO;
import com.ctrip.igt.geo.interfaces.dto.GaodeDistanceInfoDTO;
import com.ctrip.igt.geo.interfaces.message.QueryDistanceBatchRequestType;
import com.ctrip.igt.geo.interfaces.message.QueryDistanceBatchResponseType;
import com.github.benmanes.caffeine.cache.Cache;
import com.google.common.collect.Lists;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * GeoGatewayImpl新接口切换逻辑测试
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class GeoGatewayImplNewApiTest {

    @Mock
    private OchGeoServiceProxy ochGeoServiceProxy;

    @Mock
    private DcsMapDomainServiceProxy dcsMapDomainServiceProxy;

    @Mock
    private TRocksProviderProxy trocksProviderProxy;

    @Mock
    private Cache<String, String> caffeineCache;

    @Mock
    private LbsRateLimiter rateLimiter;

    @Mock
    private DelayDspCommonQConfig delayDspCommonQConfig;

    @Mock
    private LbsBufferConfig lbsBufferConfig;

    @InjectMocks
    private GeoGatewayImpl geoGateway;

    private List<Position> positions;
    private QueryDistanceBatchResponseType mockResponse;

    @Before
    public void setUp() {
        // 准备测试数据
        positions = Lists.newArrayList(
                new Position(116.3, 39.9, 116.4, 40.0, "gcj02")
        );

        // Mock响应数据
        mockResponse = new QueryDistanceBatchResponseType();
        GaodeDistanceInfoDTO dto = new GaodeDistanceInfoDTO();
        dto.setOriginLongitude(BigDecimal.valueOf(116.3));
        dto.setOriginLatitude(BigDecimal.valueOf(39.9));
        dto.setDestinationLongitude(BigDecimal.valueOf(116.4));
        dto.setDestinationLatitude(BigDecimal.valueOf(40.0));
        dto.setDistance(5000);
        dto.setDuration(600);
        mockResponse.setResults(Lists.newArrayList(dto));

        // Mock限流器
        when(rateLimiter.acquire()).thenReturn(0.1);
    }

    @Test
    public void testQueryRoutes_UseOldApi_WhenNotTop20City() {
        // Given
        Integer cityId = 999; // 非top20城市
        when(delayDspCommonQConfig.isTop20CityForNewLbsApi(cityId)).thenReturn(false);
        when(ochGeoServiceProxy.queryDistanceBatch(any(QueryDistanceBatchRequestType.class)))
                .thenReturn(mockResponse);

        // When
        List<Route> routes = geoGateway.queryRoutes(cityId, positions);

        // Then
        assertNotNull(routes);
        assertEquals(1, routes.size());
        verify(ochGeoServiceProxy, times(1)).queryDistanceBatch(any());
        verify(dcsMapDomainServiceProxy, never()).queryEstimateRouteWithBufferTimeParallel(any());
    }

    @Test
    public void testQueryRoutes_UseOldApi_WhenNotInPeakHours() {
        // Given
        Integer cityId = 1; // top20城市
        when(delayDspCommonQConfig.isTop20CityForNewLbsApi(cityId)).thenReturn(true);
        
        // Mock非高峰期配置
        LbsBufferValueVO config = new LbsBufferValueVO("08:00", "18:00", "fixValue", 10.0);
        when(lbsBufferConfig.getValue(cityId)).thenReturn(config);
        
        when(ochGeoServiceProxy.queryDistanceBatch(any(QueryDistanceBatchRequestType.class)))
                .thenReturn(mockResponse);

        // When
        List<Route> routes = geoGateway.queryRoutes(cityId, positions);

        // Then
        assertNotNull(routes);
        assertEquals(1, routes.size());
        verify(ochGeoServiceProxy, times(1)).queryDistanceBatch(any());
    }

    @Test
    public void testQueryRoutes_UseNewApi_WhenDirectSwitchEnabled() {
        // Given
        Integer cityId = 1; // top20城市
        when(delayDspCommonQConfig.isTop20CityForNewLbsApi(cityId)).thenReturn(true);
        when(delayDspCommonQConfig.isDirectSwitchToNewLbsApi(cityId)).thenReturn(true);
        
        // Mock高峰期配置 - 当前时间在高峰期内
        LbsBufferValueVO config = new LbsBufferValueVO("00:00", "23:59", "fixValue", 10.0);
        when(lbsBufferConfig.getValue(cityId)).thenReturn(config);

        // When
        List<Route> routes = geoGateway.queryRoutes(cityId, positions);

        // Then
        assertNotNull(routes);
        // 由于新接口的实现是TODO状态，这里返回空列表
        assertEquals(0, routes.size());
        verify(ochGeoServiceProxy, never()).queryDistanceBatch(any());
    }

    @Test
    public void testQueryRoutes_UseOldApiWithAsyncMonitor_WhenAsyncMonitorEnabled() {
        // Given
        Integer cityId = 1; // top20城市
        when(delayDspCommonQConfig.isTop20CityForNewLbsApi(cityId)).thenReturn(true);
        when(delayDspCommonQConfig.isDirectSwitchToNewLbsApi(cityId)).thenReturn(false);
        when(delayDspCommonQConfig.isAsyncMonitorNewLbsApi(cityId)).thenReturn(true);
        
        // Mock高峰期配置
        LbsBufferValueVO config = new LbsBufferValueVO("00:00", "23:59", "fixValue", 10.0);
        when(lbsBufferConfig.getValue(cityId)).thenReturn(config);
        
        when(ochGeoServiceProxy.queryDistanceBatch(any(QueryDistanceBatchRequestType.class)))
                .thenReturn(mockResponse);

        // When
        List<Route> routes = geoGateway.queryRoutes(cityId, positions);

        // Then
        assertNotNull(routes);
        assertEquals(1, routes.size());
        verify(ochGeoServiceProxy, times(1)).queryDistanceBatch(any());
        // 异步监控应该被触发，但仍使用旧接口返回结果
    }

    @Test
    public void testQueryRoutes_HandleException_FallbackToOldApi() {
        // Given
        Integer cityId = 1;
        when(delayDspCommonQConfig.isTop20CityForNewLbsApi(cityId)).thenReturn(true);
        when(delayDspCommonQConfig.isDirectSwitchToNewLbsApi(cityId)).thenReturn(true);
        when(lbsBufferConfig.getValue(cityId)).thenThrow(new RuntimeException("Config error"));
        
        when(ochGeoServiceProxy.queryDistanceBatch(any(QueryDistanceBatchRequestType.class)))
                .thenReturn(mockResponse);

        // When
        List<Route> routes = geoGateway.queryRoutes(cityId, positions);

        // Then
        assertNotNull(routes);
        assertEquals(1, routes.size());
        verify(ochGeoServiceProxy, times(1)).queryDistanceBatch(any());
    }
}
