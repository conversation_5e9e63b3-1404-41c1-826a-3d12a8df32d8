# 新LBS接口切换功能说明

## 功能概述

本功能实现了从旧的`querydistancebatch`接口切换到新的`queryEstimateRouteWithBufferTimeParallel`接口的逻辑，支持基于城市和高峰期时间的智能切换。

## 实现原理

### 切换判断逻辑

1. **城市判断**：只有配置在`top20CitiesForNewLbsApi`中的城市才会进行后续判断
2. **高峰期判断**：基于`LbsBufferConfig`配置的时间段判断当前是否为高峰期
3. **开关控制**：
   - `directSwitchToNewLbsApi`：直接切换开关，开启后直接使用新接口
   - `asyncMonitorNewLbsApi`：异步监控开关，开启后异步调用新接口进行对比监控

### 优先级

```
directSwitchToNewLbsApi > asyncMonitorNewLbsApi > 使用旧接口
```

## 配置说明

### DelayDspCommonQConfig新增配置项

```properties
# Top20城市配置
top20CitiesForNewLbsApi=1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20

# 直接切换开关
directSwitchToNewLbsApi=1,2

# 异步监控开关
asyncMonitorNewLbsApi=3,4,5
```

### 高峰期时间配置

高峰期时间通过`LbsBufferConfig`配置，配置文件：`visualConfig_car.dsp.lbs_buffer_config_0.json`

示例配置：
```json
{
  "1#0#0": {
    "key": {"cityId": 1, "isWorkDay": 0, "ifOutCity": 0},
    "values": [{
      "startTime": "07:00",
      "endTime": "09:00", 
      "valueType": "fixValue",
      "value": 10.0
    }, {
      "startTime": "17:00",
      "endTime": "19:00",
      "valueType": "fixValue", 
      "value": 15.0
    }]
  }
}
```

## 代码实现

### 主要修改文件

1. **DelayDspCommonQConfig.java**
   - 新增三个配置项
   - 新增判断方法：`isTop20CityForNewLbsApi()`, `isDirectSwitchToNewLbsApi()`, `isAsyncMonitorNewLbsApi()`

2. **GeoGatewayImpl.java**
   - 修改`queryRoutes()`方法，增加切换逻辑
   - 新增`shouldUseNewLbsApi()`方法判断是否使用新接口
   - 新增`isInPeakHours()`方法判断是否在高峰期
   - 新增`queryRoutesWithNewApi()`方法调用新接口（待完善）
   - 新增`asyncMonitorNewLbsApi()`方法进行异步监控（待完善）

### 关键方法

```java
// 判断是否应该使用新接口
private boolean shouldUseNewLbsApi(Integer cityId, List<Position> positions)

// 判断是否在高峰期
private boolean isInPeakHours(Integer cityId, List<Position> positions)

// 使用新接口查询路线
private List<Route> queryRoutesWithNewApi(Integer cityId, List<Position> positions)

// 异步监控新接口
private void asyncMonitorNewLbsApi(Integer cityId, List<Position> positions)
```

## 监控指标

系统会记录以下监控指标：

- `geo.new.lbs.api.direct.switch` - 直接切换到新接口的次数
- `geo.new.lbs.api.async.monitor` - 异步监控触发的次数  
- `geo.new.lbs.api.peak.hours.match` - 高峰期匹配的次数
- `geo.new.lbs.api.call.success` - 新接口调用成功的次数
- `geo.new.lbs.api.call.error` - 新接口调用失败的次数
- `geo.new.lbs.api.fallback.to.old` - 降级到旧接口的次数
- `geo.new.lbs.api.async.monitor.triggered` - 异步监控被触发的次数

## 使用场景

### 场景1：灰度测试
```properties
top20CitiesForNewLbsApi=1,2,3,4,5
directSwitchToNewLbsApi=1
asyncMonitorNewLbsApi=2,3
```

### 场景2：全量监控
```properties
top20CitiesForNewLbsApi=1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20
directSwitchToNewLbsApi=
asyncMonitorNewLbsApi=all
```

### 场景3：逐步切换
第一阶段：
```properties
top20CitiesForNewLbsApi=1,2,3,4,5
directSwitchToNewLbsApi=
asyncMonitorNewLbsApi=1,2,3,4,5
```

第二阶段：
```properties
top20CitiesForNewLbsApi=1,2,3,4,5
directSwitchToNewLbsApi=1,2
asyncMonitorNewLbsApi=3,4,5
```

## 待完善事项

1. **新接口实现**：`queryRoutesWithNewApi()`方法中需要根据实际的`QueryEstimateRouteWithBufferTimeParallelRequestType`结构完善请求构建和响应解析逻辑

2. **异步监控实现**：`asyncMonitorNewLbsApi()`方法中需要实现真正的异步调用和结果对比逻辑

3. **异常处理**：完善各种异常场景的处理和降级逻辑

4. **性能优化**：考虑缓存、连接池等性能优化措施

## 测试

已提供单元测试：`GeoGatewayImplNewApiTest.java`，覆盖主要的切换场景。

## 部署注意事项

1. 确保新接口`DcsMapDomainServiceProxy`已正确配置和部署
2. 配置文件中的城市ID需要与实际业务城市ID保持一致
3. 高峰期时间配置需要根据实际业务需求调整
4. 建议先在测试环境验证切换逻辑，再逐步在生产环境灰度发布
