package com.ctrip.dcs.dsp.delay.infrastructure.geteway;

import com.ctrip.dcs.dsp.delay.consts.CommonConstant;
import com.ctrip.dcs.dsp.delay.gateway.GeoGateway;
import com.ctrip.dcs.dsp.delay.infrastructure.soa.DcsMapDomainServiceProxy;
import com.ctrip.dcs.dsp.delay.infrastructure.soa.OchGeoServiceProxy;
import com.ctrip.dcs.dsp.delay.infrastructure.trocks.TRocksProviderProxy;
import com.ctrip.dcs.dsp.delay.limit.LbsRateLimiter;
import com.ctrip.dcs.dsp.delay.model.Position;
import com.ctrip.dcs.dsp.delay.model.Route;
import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig;
import com.ctrip.dcs.dsp.delay.qconfig.LbsBufferConfig;
import com.ctrip.dcs.dsp.delay.qconfig.value.LbsBufferValueVO;
import com.ctrip.dcs.dsp.delay.util.DateUtil;
import com.ctrip.dcs.dsp.delay.util.GeoHashUtil;
import com.ctrip.dcs.dsp.delay.util.MetricsUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.geo.interfaces.dto.BaseLatLngPairDTO;
import com.ctrip.igt.geo.interfaces.dto.GaodeDistanceInfoDTO;
import com.ctrip.igt.geo.interfaces.message.QueryDistanceBatchRequestType;
import com.ctrip.igt.geo.interfaces.message.QueryDistanceBatchResponseType;
import com.github.benmanes.caffeine.cache.Cache;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component
public class GeoGatewayImpl implements GeoGateway {

    private static final Logger logger = LoggerFactory.getLogger(GeoGatewayImpl.class);

    @Autowired
    private OchGeoServiceProxy ochGeoServiceProxy;

    @Autowired
    private DcsMapDomainServiceProxy dcsMapDomainServiceProxy;

    @Autowired
    private TRocksProviderProxy trocksProviderProxy;

    @Autowired
    private Cache<String, String> caffeineCache;

    @Autowired
    private LbsRateLimiter rateLimiter;

    @Autowired
    private DelayDspCommonQConfig delayDspCommonQConfig;

    @Autowired
    private LbsBufferConfig lbsBufferConfig;

    @Override
    public List<Route> queryRoutes(Integer cityId, List<Position> positions) {
        List<Route> routes = Lists.newArrayList();
        List<List<Position>> partition = Lists.partition(positions, 99);
        for (List<Position> list : partition) {
            try {
                double acquire = rateLimiter.acquire();
                logger.info("GeoGatewayImpl.queryRoutes", "get token.time:{}", acquire);

                // 判断是否需要切换到新接口
                if (shouldUseNewLbsApi(cityId, list)) {
                    routes.addAll(queryRoutesWithNewApi(cityId, list));
                } else {
                    QueryDistanceBatchRequestType request = buildQueryDistanceBatchRequestType(cityId, list);
                    QueryDistanceBatchResponseType response = ochGeoServiceProxy.queryDistanceBatch(request);
                    routes.addAll(buildRoute(response));
                }
            } catch (Exception e) {
                logger.error("GeoGatewayImpl.queryRoutes", e);
            }
        }
        return routes;
    }

    @Override
    public Route queryRoute(Long taskId, Position position) {
        try {
            if (Objects.equals(position.getFromHash(), position.getToHash())) {
                return new Route(position.hash(), 0, 0);
            }
            String key = Route.toKey(taskId, position.hash());
            String v =  caffeineCache.get(key, k -> (trocksProviderProxy.get(k)));
            if (StringUtils.isBlank(v)) {
                MetricsUtil.recordValue("query.route.cache.null", 1);
                return new Route(position.hash(), Integer.MAX_VALUE, Integer.MAX_VALUE);
            }
            List<String> list = Splitter.on(CommonConstant.PLACEHOLDER).splitToList(v);
            return new Route(position.hash(), Double.valueOf(list.get(0)), Double.valueOf(list.get(1)));
        } catch (Exception e) {
            logger.error("GeoGatewayImpl.queryRoute", e);
        }
        MetricsUtil.recordValue("query.route.cache.null", 1);
        return new Route(position.hash(), Integer.MAX_VALUE, Integer.MAX_VALUE);
    }

    private QueryDistanceBatchRequestType buildQueryDistanceBatchRequestType(Integer cityId, List<Position> list) {
        List<BaseLatLngPairDTO> dtos = Lists.newArrayList();
        for (Position position : list) {
            BaseLatLngPairDTO dto = new BaseLatLngPairDTO();
            dto.setCid(cityId.longValue());
            dto.setCoordType(position.getFromCoordsys());
            dto.setOriginLongitude(BigDecimal.valueOf(position.getFromLongitude()));
            dto.setOriginLatitude(BigDecimal.valueOf(position.getFromLatitude()));
            dto.setDestinationLongitude(BigDecimal.valueOf(position.getToLongitude()));
            dto.setDestinationLatitude(BigDecimal.valueOf(position.getToLatitude()));
            dtos.add(dto);
        }
        QueryDistanceBatchRequestType request = new QueryDistanceBatchRequestType();
        request.setGpsPair(dtos);
        return request;
    }

    private List<Route> buildRoute(QueryDistanceBatchResponseType response) {
        List<Route> list = Lists.newArrayList();
        if (Objects.isNull(response) || CollectionUtils.isEmpty(response.getResults())) {
            return list;
        }
        for (GaodeDistanceInfoDTO dto : response.getResults()) {
            String fromHash = GeoHashUtil.buildGeoHash(dto.getOriginLongitude().doubleValue(), dto.getOriginLatitude().doubleValue());
            String toHash = GeoHashUtil.buildGeoHash(dto.getDestinationLongitude().doubleValue(), dto.getDestinationLatitude().doubleValue());
            Route route = new Route(Position.hash(fromHash, toHash), (double) dto.getDistance() / 1000, (double) dto.getDuration() / 60);
            list.add(route);
        }
        return list;
    }

    /**
     * 判断是否应该使用新的LBS接口
     * @param cityId 城市ID
     * @param positions 位置列表
     * @return true-使用新接口，false-使用旧接口
     */
    private boolean shouldUseNewLbsApi(Integer cityId, List<Position> positions) {
        try {
            // 1. 判断城市是否在top20列表中
            if (!delayDspCommonQConfig.isTop20CityForNewLbsApi(cityId)) {
                return false;
            }

            // 2. 判断是否在高峰期
            if (!isInPeakHours(cityId, positions)) {
                return false;
            }

            // 3. 判断是否开启直接切换
            if (delayDspCommonQConfig.isDirectSwitchToNewLbsApi(cityId)) {
                MetricsUtil.recordValue("geo.new.lbs.api.direct.switch", 1);
                return true;
            }

            // 4. 判断是否开启异步监控
            if (delayDspCommonQConfig.isAsyncMonitorNewLbsApi(cityId)) {
                // 异步调用新接口进行监控对比
                asyncMonitorNewLbsApi(cityId, positions);
                MetricsUtil.recordValue("geo.new.lbs.api.async.monitor", 1);
                return false; // 异步监控时仍使用旧接口
            }

            return false;
        } catch (Exception e) {
            logger.error("GeoGatewayImpl.shouldUseNewLbsApi error", e);
            MetricsUtil.recordValue("geo.new.lbs.api.should.use.error", 1);
            return false;
        }
    }

    /**
     * 判断是否在高峰期
     * @param cityId 城市ID
     * @param positions 位置列表
     * @return true-在高峰期，false-不在高峰期
     */
    private boolean isInPeakHours(Integer cityId, List<Position> positions) {
        try {
            LbsBufferValueVO config = lbsBufferConfig.getValue(cityId);
            if (config == null) {
                return false;
            }

            // 使用当前时间判断是否在高峰期
            java.util.Date now = new java.util.Date();
            String currentTime = DateUtil.formatDate(now, DateUtil.HOUR_MIN_FMT);

            // 判断当前时间是否在配置的高峰期时间段内
            int compareToStartTime = currentTime.compareTo(config.getStartTime());
            int compareToEndTime = currentTime.compareTo(config.getEndTime());

            boolean isInPeakTime = config.getStartTime().compareTo(config.getEndTime()) < 0
                    ? compareToStartTime >= 0 && compareToEndTime <= 0
                    : (compareToStartTime >= 0 || compareToEndTime <= 0);

            if (isInPeakTime) {
                MetricsUtil.recordValue("geo.new.lbs.api.peak.hours.match", 1);
            }

            return isInPeakTime;
        } catch (Exception e) {
            logger.error("GeoGatewayImpl.isInPeakHours error", e);
            MetricsUtil.recordValue("geo.new.lbs.api.peak.hours.error", 1);
            return false;
        }
    }

    /**
     * 使用新的LBS接口查询路线
     * @param cityId 城市ID
     * @param positions 位置列表
     * @return 路线列表
     */
    private List<Route> queryRoutesWithNewApi(Integer cityId, List<Position> positions) {
        try {
            // 由于无法获取新接口的具体结构，这里提供一个框架实现
            // 实际实现需要根据QueryEstimateRouteWithBufferTimeParallelRequestType的具体结构来构建请求

            // TODO: 构建新接口的请求对象
            // QueryEstimateRouteWithBufferTimeParallelRequestType newRequest = buildNewApiRequest(cityId, positions);
            // QueryEstimateRouteWithBufferTimeParallelResponseType newResponse = dcsMapDomainServiceProxy.queryEstimateRouteWithBufferTimeParallel(newRequest);
            // return buildRouteFromNewApi(newResponse);

            MetricsUtil.recordValue("geo.new.lbs.api.call.success", 1);
            logger.info("GeoGatewayImpl.queryRoutesWithNewApi called for cityId: {}, positions count: {}", cityId, positions.size());

            // 临时返回空列表，实际实现时需要替换
            return Lists.newArrayList();
        } catch (Exception e) {
            logger.error("GeoGatewayImpl.queryRoutesWithNewApi error", e);
            MetricsUtil.recordValue("geo.new.lbs.api.call.error", 1);

            // 出错时降级到旧接口
            try {
                QueryDistanceBatchRequestType request = buildQueryDistanceBatchRequestType(cityId, positions);
                QueryDistanceBatchResponseType response = ochGeoServiceProxy.queryDistanceBatch(request);
                MetricsUtil.recordValue("geo.new.lbs.api.fallback.to.old", 1);
                return buildRoute(response);
            } catch (Exception fallbackException) {
                logger.error("GeoGatewayImpl.queryRoutesWithNewApi fallback error", fallbackException);
                MetricsUtil.recordValue("geo.new.lbs.api.fallback.error", 1);
                return Lists.newArrayList();
            }
        }
    }

    /**
     * 异步监控新LBS接口
     * @param cityId 城市ID
     * @param positions 位置列表
     */
    private void asyncMonitorNewLbsApi(Integer cityId, List<Position> positions) {
        // 使用异步方式调用新接口进行监控对比
        // 这里可以使用@Async注解或者线程池来实现异步调用
        try {
            // 异步调用新接口
            // CompletableFuture.runAsync(() -> {
            //     try {
            //         List<Route> newApiRoutes = queryRoutesWithNewApi(cityId, positions);
            //         QueryDistanceBatchRequestType oldRequest = buildQueryDistanceBatchRequestType(cityId, positions);
            //         QueryDistanceBatchResponseType oldResponse = ochGeoServiceProxy.queryDistanceBatch(oldRequest);
            //         List<Route> oldApiRoutes = buildRoute(oldResponse);
            //
            //         // 对比两个接口的结果并记录监控指标
            //         compareApiResults(oldApiRoutes, newApiRoutes, cityId);
            //     } catch (Exception e) {
            //         logger.error("GeoGatewayImpl.asyncMonitorNewLbsApi async call error", e);
            //         MetricsUtil.recordValue("geo.new.lbs.api.async.call.error", 1);
            //     }
            // });

            MetricsUtil.recordValue("geo.new.lbs.api.async.monitor.triggered", 1);
            logger.info("GeoGatewayImpl.asyncMonitorNewLbsApi triggered for cityId: {}", cityId);
        } catch (Exception e) {
            logger.error("GeoGatewayImpl.asyncMonitorNewLbsApi error", e);
            MetricsUtil.recordValue("geo.new.lbs.api.async.monitor.error", 1);
        }
    }
}
