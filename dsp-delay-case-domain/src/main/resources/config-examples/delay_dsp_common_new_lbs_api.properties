# ??????? - ?LBS??????????
# ?????????????LBS??????

# Top20???? - ???LBS???????
# ?????ID????????"all"??????
# ???1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20
top20CitiesForNewLbsApi=1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20

# ??????LBS???????
# ?????ID????????"all"??????
# ????????????????????????
# ???????1,2??????
directSwitchToNewLbsApi=1,2

# ?????LBS???????  
# ?????ID????????"all"??????
# ?????????????????????????????????????
# ??????3,4,5??????
asyncMonitorNewLbsApi=3,4,5

# ?????
# 1. ??????????directSwitchToNewLbsApi > asyncMonitorNewLbsApi
# 2. ???top20CitiesForNewLbsApi??????????????
# 3. ????????LbsBufferConfig????????visualConfig_car.dsp.lbs_buffer_config_0.json?
# 4. ?????????????????????????

# ???????
# ??1????? - ????????????
# top20CitiesForNewLbsApi=1,2,3,4,5
# directSwitchToNewLbsApi=1
# asyncMonitorNewLbsApi=2,3

# ??2????? - ???top20????????
# top20CitiesForNewLbsApi=1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20
# directSwitchToNewLbsApi=
# asyncMonitorNewLbsApi=all

# ??3????? - ???????
# ?????
# top20CitiesForNewLbsApi=1,2,3,4,5
# directSwitchToNewLbsApi=
# asyncMonitorNewLbsApi=1,2,3,4,5
# 
# ??????????????
# top20CitiesForNewLbsApi=1,2,3,4,5
# directSwitchToNewLbsApi=1,2
# asyncMonitorNewLbsApi=3,4,5

# ???????
# geo.new.lbs.api.direct.switch - ???????????
# geo.new.lbs.api.async.monitor - ?????????
# geo.new.lbs.api.peak.hours.match - ????????
# geo.new.lbs.api.call.success - ??????????
# geo.new.lbs.api.call.error - ??????????
# geo.new.lbs.api.fallback.to.old - ?????????
# geo.new.lbs.api.async.monitor.triggered - ??????????
